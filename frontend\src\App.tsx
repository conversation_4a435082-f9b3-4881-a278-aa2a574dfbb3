/**
 * CareerFly - AI-Powered Job Application Automation Platform
 * Main App Component with Routing and State Management
 */
import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { motion, AnimatePresence } from 'framer-motion';
import { useCareerFlyStore } from './store/useCareerFlyStore';
import { careerFlyAPI } from './services/api';
import Dashboard from './components/Dashboard';
import LandingPage from './components/LandingPage';
import Navigation from './components/Navigation';
import LoadingSpinner from './components/LoadingSpinner';
import './index.css';

// Mock user for development (replace with real auth)
const MOCK_USER = {
  id: 'user_123',
  email: '<EMAIL>',
  name: 'Demo User',
  api_keys: {}
};

function App() {
  const { user, isAuthenticated, setUser, setLoading, isLoading } = useCareerFlyStore();
  const [backendStatus, setBackendStatus] = useState<'checking' | 'connected' | 'disconnected'>('checking');

  // Check backend health and initialize user
  useEffect(() => {
    checkBackendHealth();
    initializeUser();
  }, []);

  const checkBackendHealth = async () => {
    try {
      await careerFlyAPI.healthCheck();
      setBackendStatus('connected');
    } catch (error) {
      setBackendStatus('disconnected');
      console.error('Backend health check failed:', error);
    }
  };

  const initializeUser = () => {
    // For demo purposes, automatically set mock user
    // In production, this would check for stored auth tokens
    setUser(MOCK_USER);
  };

  // Loading screen
  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Backend Status Indicator */}
        <div className="fixed top-4 right-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className={`px-3 py-1 rounded-full text-xs font-medium ${
              backendStatus === 'connected'
                ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                : backendStatus === 'disconnected'
                ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
            }`}
          >
            Backend: {backendStatus}
          </motion.div>
        </div>

        {/* Navigation */}
        {isAuthenticated && <Navigation />}

        {/* Main Content */}
        <AnimatePresence mode="wait">
          <Routes>
            <Route
              path="/"
              element={
                isAuthenticated ? (
                  <Navigate to="/dashboard" replace />
                ) : (
                  <LandingPage />
                )
              }
            />
            <Route
              path="/dashboard"
              element={
                isAuthenticated ? (
                  <Dashboard />
                ) : (
                  <Navigate to="/" replace />
                )
              }
            />
            <Route
              path="/jobs"
              element={
                isAuthenticated ? (
                  <div className="p-6">
                    <h1 className="text-3xl font-bold text-white">Jobs</h1>
                    <p className="text-gray-300">Job management coming soon...</p>
                  </div>
                ) : (
                  <Navigate to="/" replace />
                )
              }
            />
            <Route
              path="/resumes"
              element={
                isAuthenticated ? (
                  <div className="p-6">
                    <h1 className="text-3xl font-bold text-white">Resumes</h1>
                    <p className="text-gray-300">Resume management coming soon...</p>
                  </div>
                ) : (
                  <Navigate to="/" replace />
                )
              }
            />
            <Route
              path="/applications"
              element={
                isAuthenticated ? (
                  <div className="p-6">
                    <h1 className="text-3xl font-bold text-white">Applications</h1>
                    <p className="text-gray-300">Application tracking coming soon...</p>
                  </div>
                ) : (
                  <Navigate to="/" replace />
                )
              }
            />
            <Route
              path="/settings"
              element={
                isAuthenticated ? (
                  <div className="p-6">
                    <h1 className="text-3xl font-bold text-white">Settings</h1>
                    <p className="text-gray-300">Settings panel coming soon...</p>
                  </div>
                ) : (
                  <Navigate to="/" replace />
                )
              }
            />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </AnimatePresence>

        {/* Toast Notifications */}
        <Toaster
          position="bottom-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'rgba(15, 23, 42, 0.9)',
              color: '#fff',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
            },
            success: {
              iconTheme: {
                primary: '#10b981',
                secondary: '#fff',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
      </div>
    </Router>
  );
}

export default App;
