"""
Job Parser Agent - Extracts and analyzes job descriptions from URLs
"""
import asyncio
import aiohttp
from typing import Dict, Any, List
from bs4 import BeautifulSoup
from langchain.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from backend.agents.base_agent import BaseAgent
from backend.database import db, Job
import re
import json


class JobParserAgent(BaseAgent):
    """Agent responsible for parsing job descriptions from URLs"""
    
    def __init__(self, llm_provider: str = "openai"):
        super().__init__("job_parser", llm_provider)
        self.parsing_prompt = self._create_parsing_prompt()
        self.parsing_chain = LLMChain(llm=self.llm, prompt=self.parsing_prompt)
    
    def _create_parsing_prompt(self) -> PromptTemplate:
        """Create the job parsing prompt template"""
        template = """
        You are an expert job description analyzer. Parse the following job posting and extract structured information.
        
        Job Posting Content:
        {job_content}
        
        Extract the following information and return as JSON:
        {{
            "title": "Job title",
            "company": "Company name",
            "location": "Job location",
            "employment_type": "Full-time/Part-time/Contract/etc",
            "experience_level": "Entry/Mid/Senior/etc",
            "salary_range": "Salary information if available",
            "description": "Brief job description (2-3 sentences)",
            "responsibilities": ["List of key responsibilities"],
            "requirements": ["List of requirements"],
            "skills": ["List of required skills and technologies"],
            "benefits": ["List of benefits if mentioned"],
            "application_deadline": "Deadline if mentioned",
            "remote_work": "Remote/Hybrid/On-site",
            "industry": "Industry sector"
        }}
        
        Be thorough but concise. If information is not available, use null or empty array.
        """
        
        return PromptTemplate(
            input_variables=["job_content"],
            template=template
        )
    
    async def _fetch_job_content(self, url: str) -> str:
        """Fetch job content from URL"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=30) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        return self._extract_text_from_html(html_content)
                    else:
                        raise Exception(f"Failed to fetch URL: HTTP {response.status}")
        except Exception as e:
            raise Exception(f"Error fetching job content: {str(e)}")
    
    def _extract_text_from_html(self, html_content: str) -> str:
        """Extract clean text from HTML"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get text and clean it
        text = soup.get_text()
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
    
    def _clean_parsed_data(self, parsed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and validate parsed data"""
        # Ensure required fields exist
        required_fields = ["title", "company", "description", "requirements", "skills"]
        for field in required_fields:
            if field not in parsed_data or parsed_data[field] is None:
                if field in ["requirements", "skills"]:
                    parsed_data[field] = []
                else:
                    parsed_data[field] = ""
        
        # Clean skills and requirements
        if isinstance(parsed_data.get("skills"), str):
            parsed_data["skills"] = [skill.strip() for skill in parsed_data["skills"].split(",")]
        
        if isinstance(parsed_data.get("requirements"), str):
            parsed_data["requirements"] = [req.strip() for req in parsed_data["requirements"].split(",")]
        
        return parsed_data
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute job parsing task"""
        url = input_data.get("url")
        if not url:
            raise ValueError("URL is required for job parsing")
        
        # Fetch job content
        job_content = await self._fetch_job_content(url)
        
        if len(job_content) < 100:
            raise ValueError("Job content too short or failed to extract properly")
        
        # Parse with LLM
        try:
            llm_response = await self.parsing_chain.arun(job_content=job_content[:8000])  # Limit content length
            
            # Try to parse JSON response
            try:
                parsed_data = json.loads(llm_response)
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract JSON from response
                json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
                if json_match:
                    parsed_data = json.loads(json_match.group())
                else:
                    raise ValueError("Failed to parse LLM response as JSON")
            
            # Clean and validate data
            parsed_data = self._clean_parsed_data(parsed_data)
            
            # Save to database
            job = Job(
                user_id=self.user_id,
                url=url,
                title=parsed_data.get("title", ""),
                company=parsed_data.get("company", ""),
                description=parsed_data.get("description", ""),
                requirements=parsed_data.get("requirements", []),
                skills=parsed_data.get("skills", []),
                status="completed",
                parsed_data=parsed_data
            )
            
            result = await db.database.jobs.insert_one(job.to_dict())
            job_id = str(result.inserted_id)
            
            return {
                "job_id": job_id,
                "parsed_data": parsed_data,
                "url": url,
                "status": "success"
            }
            
        except Exception as e:
            raise Exception(f"Failed to parse job with LLM: {str(e)}")
    
    async def get_job_by_id(self, job_id: str) -> Dict[str, Any]:
        """Get job by ID"""
        from bson import ObjectId
        job = await db.database.jobs.find_one({"_id": ObjectId(job_id)})
        return job
    
    async def get_user_jobs(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all jobs for a user"""
        cursor = db.database.jobs.find({"user_id": user_id}).sort("created_at", -1)
        jobs = []
        async for job in cursor:
            jobs.append(job)
        return jobs
