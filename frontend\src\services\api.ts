/**
 * CareerFly API Service
 * Handles all API communications with the backend
 */
import axios from 'axios';
import { toast } from 'react-hot-toast';

// API Configuration
const API_BASE_URL = 'http://127.0.0.1:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('careerfly_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const message = error.response?.data?.detail || error.message || 'An error occurred';
    toast.error(message);
    return Promise.reject(error);
  }
);

// API Types
export interface ApiResponse<T = any> {
  status: string;
  message: string;
  data?: T;
}

export interface JobParseRequest {
  url: string;
  user_id: string;
}

export interface ResumeAnalysisRequest {
  resume_id: string;
  job_id: string;
  user_id: string;
}

export interface WorkflowRequest {
  job_url: string;
  resume_id?: string;
  user_id: string;
  workflow_type: 'job_analysis' | 'resume_optimization' | 'full_application';
}

export interface UserCreateRequest {
  email: string;
  name: string;
  api_keys?: Record<string, string>;
}

// API Service Class
class CareerFlyAPI {
  // Health check
  async healthCheck() {
    const response = await api.get('/health');
    return response.data;
  }

  // User management
  async createUser(userData: UserCreateRequest): Promise<ApiResponse> {
    const response = await api.post('/api/users', userData);
    return response.data;
  }

  async getUser(userId: string) {
    const response = await api.get(`/api/users/${userId}`);
    return response.data;
  }

  // Job management
  async parseJob(request: JobParseRequest): Promise<ApiResponse> {
    const response = await api.post('/api/parse-job', request);
    return response.data;
  }

  async getUserJobs(userId: string) {
    const response = await api.get(`/api/jobs/${userId}`);
    return response.data;
  }

  async getJobDetail(jobId: string) {
    const response = await api.get(`/api/jobs/detail/${jobId}`);
    return response.data;
  }

  // Resume management
  async uploadResume(file: File, userId: string): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('user_id', userId);

    const response = await api.post('/api/upload-resume', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getUserResumes(userId: string) {
    const response = await api.get(`/api/resumes/${userId}`);
    return response.data;
  }

  async analyzeResume(request: ResumeAnalysisRequest): Promise<ApiResponse> {
    const response = await api.post('/api/analyze-resume', request);
    return response.data;
  }

  async getResumeVersions(userId: string, jobId?: string) {
    const params = jobId ? { job_id: jobId } : {};
    const response = await api.get(`/api/resume-versions/${userId}`, { params });
    return response.data;
  }

  // Workflow management
  async runWorkflow(request: WorkflowRequest): Promise<ApiResponse> {
    const response = await api.post('/api/workflows/run', request);
    return response.data;
  }

  // Agent monitoring
  async getAgentStatus(userId: string) {
    const response = await api.get(`/api/agents/status/${userId}`);
    return response.data;
  }

  async getTaskStatus(taskId: string) {
    const response = await api.get(`/api/agents/task/${taskId}`);
    return response.data;
  }

  // Application tracking
  async getUserApplications(userId: string) {
    const response = await api.get(`/api/applications/${userId}`);
    return response.data;
  }

  // Utility methods
  async pollTaskStatus(taskId: string, onUpdate?: (task: any) => void): Promise<any> {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const task = await this.getTaskStatus(taskId);
          
          if (onUpdate) {
            onUpdate(task);
          }

          if (task.status === 'completed') {
            resolve(task);
          } else if (task.status === 'failed') {
            reject(new Error(task.error_message || 'Task failed'));
          } else {
            // Continue polling
            setTimeout(poll, 2000);
          }
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }

  async runJobAnalysisWorkflow(jobUrl: string, userId: string, onUpdate?: (task: any) => void) {
    const workflowRequest: WorkflowRequest = {
      job_url: jobUrl,
      user_id: userId,
      workflow_type: 'job_analysis'
    };

    const response = await this.runWorkflow(workflowRequest);
    
    if (response.data?.task_id) {
      return this.pollTaskStatus(response.data.task_id, onUpdate);
    }
    
    return response;
  }

  async runResumeOptimizationWorkflow(
    jobUrl: string, 
    resumeId: string, 
    userId: string, 
    onUpdate?: (task: any) => void
  ) {
    const workflowRequest: WorkflowRequest = {
      job_url: jobUrl,
      resume_id: resumeId,
      user_id: userId,
      workflow_type: 'resume_optimization'
    };

    const response = await this.runWorkflow(workflowRequest);
    
    if (response.data?.task_id) {
      return this.pollTaskStatus(response.data.task_id, onUpdate);
    }
    
    return response;
  }
}

// Export singleton instance
export const careerFlyAPI = new CareerFlyAPI();
export default careerFlyAPI;
