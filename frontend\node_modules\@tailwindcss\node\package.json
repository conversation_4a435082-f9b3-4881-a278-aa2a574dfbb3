{"name": "@tailwindcss/node", "version": "4.1.7", "description": "A utility-first CSS framework for rapidly building custom user interfaces.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tailwindlabs/tailwindcss.git", "directory": "packages/@tailwindcss-node"}, "bugs": "https://github.com/tailwindlabs/tailwindcss/issues", "homepage": "https://tailwindcss.com", "files": ["dist/"], "publishConfig": {"provenance": true, "access": "public"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./require-cache": {"types": "./dist/require-cache.d.ts", "default": "./dist/require-cache.js"}, "./esm-cache-loader": {"types": "./dist/esm-cache.loader.d.mts", "default": "./dist/esm-cache.loader.mjs"}}, "dependencies": {"@ampproject/remapping": "^2.3.0", "enhanced-resolve": "^5.18.1", "jiti": "^2.4.2", "lightningcss": "1.30.1", "magic-string": "^0.30.17", "source-map-js": "^1.2.1", "tailwindcss": "4.1.7"}, "scripts": {"build": "tsup-node", "dev": "pnpm run build -- --watch"}}