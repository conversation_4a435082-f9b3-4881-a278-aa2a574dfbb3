"""
CareerFly - AI-Powered Job Application Automation Platform
Main FastAPI Application
"""
from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Form, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
import asyncio
import aiofiles
import os
from datetime import datetime

# Import backend modules
from backend.config import settings
from backend.database import db, User, Job, Resume, Application
from backend.agents.base_agent import agent_manager
from backend.agents.job_parser_agent import JobParserAgent
from backend.agents.resume_analyzer_agent import ResumeAnalyzerAgent
from backend.agents.crew_orchestrator import crew_orchestrator

# Create FastAPI app
app = FastAPI(
    title="CareerFly AI Agent Platform",
    description="AI-powered job application automation with LangChain and Crew AI",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for API
class JobParseRequest(BaseModel):
    url: str
    user_id: str

class ResumeAnalysisRequest(BaseModel):
    resume_id: str
    job_id: str
    user_id: str

class WorkflowRequest(BaseModel):
    job_url: str
    resume_id: Optional[str] = None
    user_id: str
    workflow_type: str  # "job_analysis", "resume_optimization", "full_application"

class UserCreate(BaseModel):
    email: str
    name: str
    api_keys: Optional[Dict[str, str]] = {}

class ApiResponse(BaseModel):
    status: str
    message: str
    data: Optional[Dict[str, Any]] = None

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize database connection and register agents"""
    await db.connect_db()

    # Register agents with the agent manager
    agent_manager.register_agent("job_parser", JobParserAgent)
    agent_manager.register_agent("resume_analyzer", ResumeAnalyzerAgent)

    print("CareerFly backend started successfully!")

@app.on_event("shutdown")
async def shutdown_event():
    """Close database connection"""
    await db.close_db()
    print("CareerFly backend shutdown complete!")

# Health and status endpoints
@app.get("/")
async def root():
    return {
        "message": "CareerFly AI Agent Platform is running!",
        "status": "success",
        "version": "1.0.0",
        "features": [
            "Job Description Parsing",
            "Resume Analysis & Optimization",
            "ATS Optimization",
            "Application Automation",
            "LinkedIn Outreach"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        await db.database.command("ping")
        db_status = "connected"
    except Exception:
        db_status = "disconnected"

    return {
        "status": "healthy",
        "message": "CareerFly backend is operational",
        "database": db_status,
        "timestamp": datetime.utcnow().isoformat()
    }

# User management endpoints
@app.post("/api/users", response_model=ApiResponse)
async def create_user(user_data: UserCreate):
    """Create a new user"""
    try:
        # Check if user already exists
        existing_user = await db.database.users.find_one({"email": user_data.email})
        if existing_user:
            raise HTTPException(status_code=400, detail="User already exists")

        user = User(
            email=user_data.email,
            name=user_data.name,
            api_keys=user_data.api_keys
        )

        result = await db.database.users.insert_one(user.to_dict())
        user_id = str(result.inserted_id)

        return ApiResponse(
            status="success",
            message="User created successfully",
            data={"user_id": user_id}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/users/{user_id}")
async def get_user(user_id: str):
    """Get user by ID"""
    try:
        from bson import ObjectId
        user = await db.database.users.find_one({"_id": ObjectId(user_id)})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        return user
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Job parsing endpoints
@app.post("/api/parse-job", response_model=ApiResponse)
async def parse_job(request: JobParseRequest):
    """Parse job description from URL"""
    try:
        result = await agent_manager.run_agent(
            "job_parser",
            request.user_id,
            {"url": request.url}
        )

        return ApiResponse(
            status="success",
            message="Job parsed successfully",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/jobs/{user_id}")
async def get_user_jobs(user_id: str):
    """Get all jobs for a user"""
    try:
        jobs = await db.database.jobs.find({"user_id": user_id}).sort("created_at", -1).to_list(100)
        return {"jobs": jobs}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/jobs/detail/{job_id}")
async def get_job_detail(job_id: str):
    """Get detailed job information"""
    try:
        from bson import ObjectId
        job = await db.database.jobs.find_one({"_id": ObjectId(job_id)})
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        return job
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Resume management endpoints
@app.post("/api/upload-resume")
async def upload_resume(
    file: UploadFile = File(...),
    user_id: str = Form(...)
):
    """Upload and process resume file"""
    try:
        # Validate file type
        if not file.filename.endswith(('.pdf', '.docx', '.txt')):
            raise HTTPException(status_code=400, detail="Unsupported file type")

        # Save file
        file_path = os.path.join(settings.UPLOAD_DIR, f"{user_id}_{file.filename}")
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)

        # Extract text content (simplified - would use proper parsers in production)
        if file.filename.endswith('.txt'):
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                text_content = await f.read()
        else:
            # For PDF/DOCX, you'd use libraries like PyPDF2, python-docx
            text_content = "Resume content extracted from file"  # Placeholder

        # Save to database
        resume = Resume(
            user_id=user_id,
            filename=file.filename,
            content=text_content,
            file_path=file_path
        )

        result = await db.database.resumes.insert_one(resume.to_dict())
        resume_id = str(result.inserted_id)

        return ApiResponse(
            status="success",
            message="Resume uploaded successfully",
            data={"resume_id": resume_id, "filename": file.filename}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/resumes/{user_id}")
async def get_user_resumes(user_id: str):
    """Get all resumes for a user"""
    try:
        resumes = await db.database.resumes.find({"user_id": user_id}).sort("created_at", -1).to_list(100)
        return {"resumes": resumes}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Resume analysis endpoints
@app.post("/api/analyze-resume", response_model=ApiResponse)
async def analyze_resume(request: ResumeAnalysisRequest):
    """Analyze resume against job requirements"""
    try:
        result = await agent_manager.run_agent(
            "resume_analyzer",
            request.user_id,
            {
                "resume_id": request.resume_id,
                "job_id": request.job_id
            }
        )

        return ApiResponse(
            status="success",
            message="Resume analyzed successfully",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/resume-versions/{user_id}")
async def get_resume_versions(user_id: str, job_id: str = None):
    """Get resume versions for a user"""
    try:
        query = {"user_id": user_id}
        if job_id:
            query["job_id"] = job_id

        versions = await db.database.resumes.find(query).sort("version", -1).to_list(100)
        return {"versions": versions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Crew AI workflow endpoints
@app.post("/api/workflows/run", response_model=ApiResponse)
async def run_workflow(request: WorkflowRequest):
    """Run AI agent workflows using Crew AI"""
    try:
        if request.workflow_type == "job_analysis":
            result = await crew_orchestrator.run_job_analysis_workflow(
                request.job_url,
                request.user_id
            )
        elif request.workflow_type == "resume_optimization":
            if not request.resume_id:
                raise HTTPException(status_code=400, detail="resume_id required for resume optimization")

            # Get job and resume data
            from bson import ObjectId
            job = await db.database.jobs.find_one({"url": request.job_url, "user_id": request.user_id})
            resume = await db.database.resumes.find_one({"_id": ObjectId(request.resume_id)})

            if not job or not resume:
                raise HTTPException(status_code=404, detail="Job or resume not found")

            result = await crew_orchestrator.run_resume_optimization_workflow(
                job.get("parsed_data", {}),
                resume.get("content", ""),
                request.user_id
            )
        elif request.workflow_type == "full_application":
            # Full application workflow
            result = {"status": "success", "message": "Full application workflow not yet implemented"}
        else:
            raise HTTPException(status_code=400, detail="Invalid workflow type")

        return ApiResponse(
            status="success",
            message=f"{request.workflow_type} workflow completed",
            data=result
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Agent status and monitoring endpoints
@app.get("/api/agents/status/{user_id}")
async def get_agent_status(user_id: str):
    """Get status of all agents for a user"""
    try:
        tasks = await agent_manager.get_user_tasks(user_id)
        return {"tasks": tasks}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/agents/task/{task_id}")
async def get_task_status(task_id: str):
    """Get specific task status"""
    try:
        task = await db.database.agent_logs.find_one({"task_id": task_id})
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        return task
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Application tracking endpoints
@app.get("/api/applications/{user_id}")
async def get_user_applications(user_id: str):
    """Get all applications for a user"""
    try:
        applications = await db.database.applications.find({"user_id": user_id}).sort("created_at", -1).to_list(100)
        return {"applications": applications}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
