"""
Base Agent Class for CareerFly AI Agents
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime
import asyncio
import uuid
from langchain.llms.base import LLM
from langchain_openai import ChatOpenAI
from langchain_community.llms import Cohere
from backend.config import settings
from backend.database import db, AgentLog


class BaseAgent(ABC):
    """Base class for all CareerFly AI agents"""
    
    def __init__(self, agent_type: str, llm_provider: str = "openai"):
        self.agent_type = agent_type
        self.llm_provider = llm_provider
        self.llm = self._initialize_llm()
        self.task_id = None
        self.user_id = None
        
    def _initialize_llm(self) -> LLM:
        """Initialize the LLM based on provider"""
        if self.llm_provider == "openai":
            return ChatOpenAI(
                api_key=settings.OPENAI_API_KEY,
                model="gpt-4",
                temperature=0.1
            )
        elif self.llm_provider == "cohere":
            return Cohere(
                cohere_api_key=settings.COHERE_API_KEY,
                model="command",
                temperature=0.1
            )
        else:
            raise ValueError(f"Unsupported LLM provider: {self.llm_provider}")
    
    async def start_task(self, user_id: str, input_data: Dict[str, Any]) -> str:
        """Start a new task and return task ID"""
        self.task_id = str(uuid.uuid4())
        self.user_id = user_id
        
        # Log task start
        log = AgentLog(
            user_id=user_id,
            agent_type=self.agent_type,
            task_id=self.task_id,
            status="running",
            input_data=input_data
        )
        
        await db.database.agent_logs.insert_one(log.to_dict())
        return self.task_id
    
    async def complete_task(self, output_data: Dict[str, Any], execution_time: float):
        """Mark task as completed"""
        if self.task_id:
            await db.database.agent_logs.update_one(
                {"task_id": self.task_id},
                {
                    "$set": {
                        "status": "completed",
                        "output_data": output_data,
                        "execution_time": execution_time,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
    
    async def fail_task(self, error_message: str, execution_time: float):
        """Mark task as failed"""
        if self.task_id:
            await db.database.agent_logs.update_one(
                {"task_id": self.task_id},
                {
                    "$set": {
                        "status": "failed",
                        "error_message": error_message,
                        "execution_time": execution_time,
                        "updated_at": datetime.utcnow()
                    }
                }
            )
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get task status by ID"""
        log = await db.database.agent_logs.find_one({"task_id": task_id})
        return log
    
    @abstractmethod
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the agent's main task"""
        pass
    
    async def run(self, user_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run the agent with error handling and logging"""
        start_time = datetime.utcnow()
        task_id = await self.start_task(user_id, input_data)
        
        try:
            result = await self.execute(input_data)
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            await self.complete_task(result, execution_time)
            
            return {
                "task_id": task_id,
                "status": "completed",
                "result": result,
                "execution_time": execution_time
            }
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            error_message = str(e)
            await self.fail_task(error_message, execution_time)
            
            return {
                "task_id": task_id,
                "status": "failed",
                "error": error_message,
                "execution_time": execution_time
            }


class AgentManager:
    """Manages all AI agents and their tasks"""
    
    def __init__(self):
        self.agents = {}
        self.running_tasks = {}
    
    def register_agent(self, agent_type: str, agent_class):
        """Register an agent type"""
        self.agents[agent_type] = agent_class
    
    async def create_agent(self, agent_type: str, llm_provider: str = "openai") -> BaseAgent:
        """Create an agent instance"""
        if agent_type not in self.agents:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        return self.agents[agent_type](llm_provider=llm_provider)
    
    async def run_agent(self, agent_type: str, user_id: str, input_data: Dict[str, Any], 
                       llm_provider: str = "openai") -> Dict[str, Any]:
        """Run an agent task"""
        agent = await self.create_agent(agent_type, llm_provider)
        result = await agent.run(user_id, input_data)
        return result
    
    async def get_user_tasks(self, user_id: str, agent_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all tasks for a user"""
        query = {"user_id": user_id}
        if agent_type:
            query["agent_type"] = agent_type
        
        cursor = db.database.agent_logs.find(query).sort("created_at", -1)
        tasks = []
        async for task in cursor:
            tasks.append(task)
        return tasks


# Global agent manager
agent_manager = AgentManager()
