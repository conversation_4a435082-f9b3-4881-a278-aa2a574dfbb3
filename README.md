# 🚀 CareerFly - AI-Powered Job Application Automation Platform

**Turn Job Links Into Offers—Autonomously**

CareerFly is a cutting-edge AI agent platform that automates the entire job application process using **LangChain** and **Crew AI**. From parsing job descriptions to optimizing resumes and submitting applications, our intelligent agents work 24/7 to land you your dream job.

## ✨ Features

### 🤖 AI Agent Ecosystem
- **Job Parser Agent**: Extracts and analyzes job requirements from any URL
- **Resume Analyzer Agent**: Optimizes resumes for specific job requirements
- **ATS Optimizer Agent**: Ensures compatibility with Applicant Tracking Systems
- **Application Assistant Agent**: Automates form filling and submission
- **Outreach Specialist Agent**: Crafts personalized LinkedIn messages and cover letters

### 🎯 Core Capabilities
- **Smart Job Parsing**: Extract structured data from any job posting URL
- **AI Resume Optimization**: Tailored resume versions for each application
- **ATS Compatibility**: Automated formatting and keyword optimization
- **Application Automation**: End-to-end application submission
- **LinkedIn Outreach**: Personalized cold messages and networking
- **Real-time Monitoring**: Live agent status and task tracking

### 🎨 Modern UI/UX
- **Glassmorphism Design**: Futuristic, translucent interface elements
- **Real-time Agent Status**: Live indicators for active AI agents
- **Animated Interactions**: Smooth Framer Motion animations
- **Responsive Design**: Works perfectly on all devices
- **Dark Theme**: Eye-friendly dark mode interface

## 🏗️ Architecture

### Backend Stack
- **FastAPI**: High-performance Python web framework
- **LangChain**: LLM orchestration and chain management
- **Crew AI**: Multi-agent collaboration framework
- **MongoDB**: Document database for flexible data storage
- **OpenAI/Cohere**: Large Language Model providers

### Frontend Stack
- **React 19**: Latest React with concurrent features
- **TypeScript**: Type-safe development
- **Vite**: Lightning-fast build tool
- **TailwindCSS**: Utility-first CSS framework
- **Zustand**: Lightweight state management
- **Framer Motion**: Smooth animations and transitions

### AI Integration
- **LangChain Chains**: Custom chains for job parsing and resume analysis
- **Crew AI Tasks**: Coordinated multi-agent workflows
- **OpenAI GPT-4**: Primary language model for intelligent processing
- **Custom Tools**: Web scraping, document processing, and API integrations

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Node.js 18+
- MongoDB (local or cloud)
- OpenAI API key

### 1. Clone the Repository
```bash
git clone https://github.com/your-username/careerfly.git
cd careerfly
```

### 2. Backend Setup
```bash
# Create and activate virtual environment
python -m venv backend_env
source backend_env/bin/activate  # On Windows: backend_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys
```

### 3. Frontend Setup
```bash
cd frontend
npm install
```

### 4. Database Setup
```bash
# Start MongoDB (if running locally)
mongod

# The application will automatically create collections on first run
```

### 5. Start the Application
```bash
# Terminal 1: Start backend
python main.py

# Terminal 2: Start frontend
cd frontend
npm run dev
```

Visit `http://localhost:5173` to access CareerFly!

## 📋 API Endpoints

### Job Management
- `POST /api/parse-job` - Parse job from URL
- `GET /api/jobs/{user_id}` - Get user's jobs
- `GET /api/jobs/detail/{job_id}` - Get job details

### Resume Management
- `POST /api/upload-resume` - Upload resume file
- `GET /api/resumes/{user_id}` - Get user's resumes
- `POST /api/analyze-resume` - Analyze resume against job

### AI Workflows
- `POST /api/workflows/run` - Execute AI agent workflows
- `GET /api/agents/status/{user_id}` - Get agent status
- `GET /api/agents/task/{task_id}` - Get task details

### User Management
- `POST /api/users` - Create user
- `GET /api/users/{user_id}` - Get user details

## 🤖 Agent Workflows

### Job Analysis Workflow
1. **Job Parser Agent** extracts job requirements
2. **Company Intel Agent** researches company information
3. **Market Analysis Agent** provides salary and competition insights

### Resume Optimization Workflow
1. **Resume Analyzer Agent** compares resume to job requirements
2. **ATS Optimizer Agent** improves formatting and keywords
3. **Content Optimizer Agent** enhances descriptions and achievements

### Application Workflow
1. **Application Assistant Agent** prepares application materials
2. **Outreach Specialist Agent** crafts cover letter and LinkedIn message
3. **Submission Agent** handles form filling and submission

## 🔧 Configuration

### Environment Variables
```env
# Required
OPENAI_API_KEY=your_openai_api_key
MONGODB_URL=mongodb://localhost:27017

# Optional
COHERE_API_KEY=your_cohere_api_key
GOOGLE_API_KEY=your_google_api_key
LINKEDIN_API_KEY=your_linkedin_api_key
```

### Agent Configuration
```python
# Customize agent behavior in backend/config.py
MAX_CONCURRENT_AGENTS=5
AGENT_TIMEOUT=300
```

## 📊 Monitoring & Analytics

- **Real-time Agent Status**: Monitor active agents and their tasks
- **Task Execution Logs**: Detailed logs of all agent activities
- **Performance Metrics**: Track success rates and execution times
- **Error Handling**: Comprehensive error tracking and recovery

## 🛠️ Development

### Adding New Agents
1. Create agent class in `backend/agents/`
2. Inherit from `BaseAgent`
3. Implement `execute()` method
4. Register with `AgentManager`

### Custom LangChain Chains
1. Define chain in agent class
2. Use appropriate prompt templates
3. Integrate with LLM providers

### Frontend Components
1. Create component in `frontend/src/components/`
2. Use TypeScript for type safety
3. Implement responsive design with TailwindCSS

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LangChain** for LLM orchestration
- **Crew AI** for multi-agent frameworks
- **OpenAI** for GPT-4 capabilities
- **Anthropic** for Claude integration
- **Vercel** for deployment platform

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/careerfly)
- 📖 Documentation: [docs.careerfly.ai](https://docs.careerfly.ai)

---

**Built with ❤️ by the CareerFly Team**

*Empowering job seekers with AI automation*
