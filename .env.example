# CareerFly Environment Configuration
# Copy this file to .env and fill in your API keys

# OpenAI API Key (required for LLM functionality)
OPENAI_API_KEY=your_openai_api_key_here

# Cohere API Key (optional, alternative LLM provider)
COHERE_API_KEY=your_cohere_api_key_here

# Google API Key (for search and other Google services)
GOOGLE_API_KEY=your_google_api_key_here

# LinkedIn API Key (for outreach functionality)
LINKEDIN_API_KEY=your_linkedin_api_key_here

# Database Configuration
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=careerfly

# FastAPI Settings
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true

# CORS Settings (comma-separated list)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:5174

# Agent Configuration
MAX_CONCURRENT_AGENTS=5
AGENT_TIMEOUT=300

# File Upload Settings
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Web Scraping Settings
SELENIUM_TIMEOUT=30
REQUEST_DELAY=1.0
