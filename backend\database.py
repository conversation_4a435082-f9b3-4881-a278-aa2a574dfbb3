"""
MongoDB Database Connection and Models
"""
from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import MongoClient
from typing import Optional, Dict, Any, List
from datetime import datetime
from bson import ObjectId
from backend.config import settings
import asyncio


class Database:
    client: Optional[AsyncIOMotorClient] = None
    database = None

    @classmethod
    async def connect_db(cls):
        """Create database connection"""
        cls.client = AsyncIOMotorClient(settings.MONGODB_URL)
        cls.database = cls.client[settings.DATABASE_NAME]
        
        # Create indexes
        await cls.create_indexes()
        
    @classmethod
    async def close_db(cls):
        """Close database connection"""
        if cls.client:
            cls.client.close()
            
    @classmethod
    async def create_indexes(cls):
        """Create database indexes for better performance"""
        if cls.database:
            # Users collection indexes
            await cls.database.users.create_index("email", unique=True)
            
            # Jobs collection indexes
            await cls.database.jobs.create_index("user_id")
            await cls.database.jobs.create_index("status")
            await cls.database.jobs.create_index("created_at")
            
            # Applications collection indexes
            await cls.database.applications.create_index("user_id")
            await cls.database.applications.create_index("job_id")
            await cls.database.applications.create_index("status")
            
            # Agent logs collection indexes
            await cls.database.agent_logs.create_index("user_id")
            await cls.database.agent_logs.create_index("agent_type")
            await cls.database.agent_logs.create_index("created_at")


# Database models
class BaseModel:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
        if not hasattr(self, '_id'):
            self._id = None
        if not hasattr(self, 'created_at'):
            self.created_at = datetime.utcnow()
        if not hasattr(self, 'updated_at'):
            self.updated_at = datetime.utcnow()
    
    def to_dict(self) -> Dict[str, Any]:
        result = {}
        for key, value in self.__dict__.items():
            if isinstance(value, ObjectId):
                result[key] = str(value)
            elif isinstance(value, datetime):
                result[key] = value.isoformat()
            else:
                result[key] = value
        return result


class User(BaseModel):
    def __init__(self, email: str, name: str, **kwargs):
        super().__init__(**kwargs)
        self.email = email
        self.name = name
        self.api_keys = kwargs.get('api_keys', {})
        self.preferences = kwargs.get('preferences', {})


class Job(BaseModel):
    def __init__(self, user_id: str, url: str, title: str, company: str, **kwargs):
        super().__init__(**kwargs)
        self.user_id = user_id
        self.url = url
        self.title = title
        self.company = company
        self.description = kwargs.get('description', '')
        self.requirements = kwargs.get('requirements', [])
        self.skills = kwargs.get('skills', [])
        self.status = kwargs.get('status', 'pending')  # pending, processing, completed, failed
        self.parsed_data = kwargs.get('parsed_data', {})


class Resume(BaseModel):
    def __init__(self, user_id: str, filename: str, content: str, **kwargs):
        super().__init__(**kwargs)
        self.user_id = user_id
        self.filename = filename
        self.content = content
        self.file_path = kwargs.get('file_path', '')
        self.version = kwargs.get('version', 1)
        self.job_id = kwargs.get('job_id', None)  # If optimized for specific job
        self.is_active = kwargs.get('is_active', True)


class Application(BaseModel):
    def __init__(self, user_id: str, job_id: str, resume_id: str, **kwargs):
        super().__init__(**kwargs)
        self.user_id = user_id
        self.job_id = job_id
        self.resume_id = resume_id
        self.status = kwargs.get('status', 'draft')  # draft, submitted, rejected, interview
        self.cover_letter = kwargs.get('cover_letter', '')
        self.application_data = kwargs.get('application_data', {})
        self.submitted_at = kwargs.get('submitted_at', None)


class AgentLog(BaseModel):
    def __init__(self, user_id: str, agent_type: str, task_id: str, **kwargs):
        super().__init__(**kwargs)
        self.user_id = user_id
        self.agent_type = agent_type  # job_parser, resume_analyzer, etc.
        self.task_id = task_id
        self.status = kwargs.get('status', 'running')  # running, completed, failed
        self.input_data = kwargs.get('input_data', {})
        self.output_data = kwargs.get('output_data', {})
        self.error_message = kwargs.get('error_message', None)
        self.execution_time = kwargs.get('execution_time', None)


# Database instance
db = Database()
