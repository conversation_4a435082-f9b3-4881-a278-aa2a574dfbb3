"""
Resume Analyzer Agent - Analyzes and optimizes resumes for specific jobs
"""
import asyncio
from typing import Dict, Any, <PERSON>, Tuple
from langchain.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON><PERSON>n
from backend.agents.base_agent import BaseAgent
from backend.database import db, Resume
import json
import re
from difflib import SequenceMatcher


class ResumeAnalyzerAgent(BaseAgent):
    """Agent responsible for analyzing and optimizing resumes"""
    
    def __init__(self, llm_provider: str = "openai"):
        super().__init__("resume_analyzer", llm_provider)
        self.analysis_prompt = self._create_analysis_prompt()
        self.optimization_prompt = self._create_optimization_prompt()
        self.analysis_chain = LLMChain(llm=self.llm, prompt=self.analysis_prompt)
        self.optimization_chain = LLMChain(llm=self.llm, prompt=self.optimization_prompt)
    
    def _create_analysis_prompt(self) -> PromptTemplate:
        """Create the resume analysis prompt template"""
        template = """
        You are an expert resume analyst and career coach. Analyze the following resume against the job requirements.
        
        RESUME CONTENT:
        {resume_content}
        
        JOB REQUIREMENTS:
        Title: {job_title}
        Company: {job_company}
        Required Skills: {job_skills}
        Requirements: {job_requirements}
        Description: {job_description}
        
        Provide a detailed analysis in JSON format:
        {{
            "overall_match_score": 85,
            "strengths": ["List of resume strengths relevant to the job"],
            "weaknesses": ["List of areas that need improvement"],
            "missing_skills": ["Skills mentioned in job but missing from resume"],
            "keyword_analysis": {{
                "matched_keywords": ["keywords found in both resume and job"],
                "missing_keywords": ["important keywords missing from resume"],
                "keyword_density_score": 75
            }},
            "section_analysis": {{
                "summary": {{"score": 80, "feedback": "Feedback on summary section"}},
                "experience": {{"score": 85, "feedback": "Feedback on experience section"}},
                "skills": {{"score": 70, "feedback": "Feedback on skills section"}},
                "education": {{"score": 90, "feedback": "Feedback on education section"}}
            }},
            "ats_compatibility": {{
                "score": 85,
                "issues": ["List of ATS compatibility issues"],
                "recommendations": ["List of ATS optimization recommendations"]
            }},
            "recommendations": ["Specific actionable recommendations for improvement"]
        }}
        
        Be specific and actionable in your feedback.
        """
        
        return PromptTemplate(
            input_variables=["resume_content", "job_title", "job_company", "job_skills", "job_requirements", "job_description"],
            template=template
        )
    
    def _create_optimization_prompt(self) -> PromptTemplate:
        """Create the resume optimization prompt template"""
        template = """
        You are an expert resume writer. Based on the analysis, create an optimized version of the resume.
        
        ORIGINAL RESUME:
        {resume_content}
        
        ANALYSIS RESULTS:
        {analysis_results}
        
        JOB DETAILS:
        Title: {job_title}
        Company: {job_company}
        Required Skills: {job_skills}
        Requirements: {job_requirements}
        
        Create an optimized resume that:
        1. Addresses the weaknesses identified in the analysis
        2. Incorporates missing keywords naturally
        3. Improves ATS compatibility
        4. Maintains truthfulness and authenticity
        5. Follows best practices for resume formatting
        
        Return the optimized resume in the same format as the original, but improved.
        Also provide a summary of changes made.
        
        Response format:
        {{
            "optimized_resume": "The complete optimized resume content",
            "changes_summary": ["List of specific changes made"],
            "improvement_score": 92
        }}
        """
        
        return PromptTemplate(
            input_variables=["resume_content", "analysis_results", "job_title", "job_company", "job_skills", "job_requirements"],
            template=template
        )
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts"""
        return SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        # Simple keyword extraction - can be enhanced with NLP libraries
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        # Filter out common words
        common_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
        keywords = [word for word in set(words) if word not in common_words and len(word) > 3]
        return keywords
    
    async def execute(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute resume analysis and optimization"""
        resume_id = input_data.get("resume_id")
        job_id = input_data.get("job_id")
        
        if not resume_id or not job_id:
            raise ValueError("Both resume_id and job_id are required")
        
        # Fetch resume and job data
        from bson import ObjectId
        resume = await db.database.resumes.find_one({"_id": ObjectId(resume_id)})
        job = await db.database.jobs.find_one({"_id": ObjectId(job_id)})
        
        if not resume or not job:
            raise ValueError("Resume or job not found")
        
        # Prepare data for analysis
        resume_content = resume.get("content", "")
        job_data = job.get("parsed_data", {})
        
        job_title = job_data.get("title", "")
        job_company = job_data.get("company", "")
        job_skills = ", ".join(job_data.get("skills", []))
        job_requirements = ", ".join(job_data.get("requirements", []))
        job_description = job_data.get("description", "")
        
        # Perform analysis
        try:
            analysis_response = await self.analysis_chain.arun(
                resume_content=resume_content,
                job_title=job_title,
                job_company=job_company,
                job_skills=job_skills,
                job_requirements=job_requirements,
                job_description=job_description
            )
            
            # Parse analysis results
            try:
                analysis_results = json.loads(analysis_response)
            except json.JSONDecodeError:
                json_match = re.search(r'\{.*\}', analysis_response, re.DOTALL)
                if json_match:
                    analysis_results = json.loads(json_match.group())
                else:
                    raise ValueError("Failed to parse analysis response")
            
            # Perform optimization
            optimization_response = await self.optimization_chain.arun(
                resume_content=resume_content,
                analysis_results=json.dumps(analysis_results, indent=2),
                job_title=job_title,
                job_company=job_company,
                job_skills=job_skills,
                job_requirements=job_requirements
            )
            
            # Parse optimization results
            try:
                optimization_results = json.loads(optimization_response)
            except json.JSONDecodeError:
                json_match = re.search(r'\{.*\}', optimization_response, re.DOTALL)
                if json_match:
                    optimization_results = json.loads(json_match.group())
                else:
                    raise ValueError("Failed to parse optimization response")
            
            # Save optimized resume as new version
            optimized_resume = Resume(
                user_id=self.user_id,
                filename=f"optimized_{resume['filename']}",
                content=optimization_results.get("optimized_resume", ""),
                job_id=job_id,
                version=resume.get("version", 1) + 1,
                is_active=False  # Don't make it active by default
            )
            
            result = await db.database.resumes.insert_one(optimized_resume.to_dict())
            optimized_resume_id = str(result.inserted_id)
            
            return {
                "analysis": analysis_results,
                "optimization": optimization_results,
                "original_resume_id": resume_id,
                "optimized_resume_id": optimized_resume_id,
                "job_id": job_id,
                "status": "success"
            }
            
        except Exception as e:
            raise Exception(f"Failed to analyze/optimize resume: {str(e)}")
    
    async def get_resume_versions(self, user_id: str, job_id: str = None) -> List[Dict[str, Any]]:
        """Get all resume versions for a user, optionally filtered by job"""
        query = {"user_id": user_id}
        if job_id:
            query["job_id"] = job_id
        
        cursor = db.database.resumes.find(query).sort("created_at", -1)
        resumes = []
        async for resume in cursor:
            resumes.append(resume)
        return resumes
    
    async def compare_resumes(self, resume_id1: str, resume_id2: str) -> Dict[str, Any]:
        """Compare two resume versions"""
        from bson import ObjectId
        
        resume1 = await db.database.resumes.find_one({"_id": ObjectId(resume_id1)})
        resume2 = await db.database.resumes.find_one({"_id": ObjectId(resume_id2)})
        
        if not resume1 or not resume2:
            raise ValueError("One or both resumes not found")
        
        content1 = resume1.get("content", "")
        content2 = resume2.get("content", "")
        
        similarity_score = self._calculate_similarity(content1, content2)
        
        keywords1 = set(self._extract_keywords(content1))
        keywords2 = set(self._extract_keywords(content2))
        
        added_keywords = keywords2 - keywords1
        removed_keywords = keywords1 - keywords2
        
        return {
            "similarity_score": similarity_score,
            "added_keywords": list(added_keywords),
            "removed_keywords": list(removed_keywords),
            "word_count_change": len(content2.split()) - len(content1.split()),
            "resume1_info": {
                "id": resume_id1,
                "filename": resume1.get("filename", ""),
                "version": resume1.get("version", 1),
                "created_at": resume1.get("created_at")
            },
            "resume2_info": {
                "id": resume_id2,
                "filename": resume2.get("filename", ""),
                "version": resume2.get("version", 1),
                "created_at": resume2.get("created_at")
            }
        }
