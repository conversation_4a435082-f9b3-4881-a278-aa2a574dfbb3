"""
Crew AI Orchestrator - Manages multi-agent workflows for CareerFly
"""
import asyncio
from typing import Dict, Any, List, Optional
from crewai import Agent, Task, Crew, Process
from langchain_openai import ChatOpenAI
from backend.config import settings
from backend.agents.base_agent import agent_manager
from backend.agents.job_parser_agent import JobParserAgent
from backend.agents.resume_analyzer_agent import ResumeAnalyzerAgent
from backend.database import db
import json


class CareerFlyCrew:
    """Main orchestrator for CareerFly AI agents using Crew AI"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            api_key=settings.OPENAI_API_KEY,
            model="gpt-4",
            temperature=0.1
        )
        self.agents = {}
        self.crews = {}
        self._initialize_agents()
    
    def _initialize_agents(self):
        """Initialize all Crew AI agents"""
        
        # Job Parser Agent
        self.agents['job_parser'] = Agent(
            role='Job Description Parser',
            goal='Extract and analyze job requirements from job postings',
            backstory="""You are an expert at reading job descriptions and extracting 
            key information like required skills, experience level, and responsibilities. 
            You have years of experience in recruitment and understand what employers 
            are really looking for.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
        
        # Resume Analyzer Agent
        self.agents['resume_analyzer'] = Agent(
            role='Resume Optimization Specialist',
            goal='Analyze resumes and optimize them for specific job requirements',
            backstory="""You are a professional resume writer and career coach with 
            extensive experience in helping candidates land their dream jobs. You 
            understand ATS systems and know how to make resumes stand out while 
            maintaining authenticity.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
        
        # ATS Optimizer Agent
        self.agents['ats_optimizer'] = Agent(
            role='ATS Optimization Expert',
            goal='Ensure resumes are optimized for Applicant Tracking Systems',
            backstory="""You are an expert in ATS systems and understand exactly 
            how they parse and rank resumes. You know the technical requirements 
            for formatting, keywords, and structure that will help resumes pass 
            through ATS filters.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
        
        # Application Assistant Agent
        self.agents['application_assistant'] = Agent(
            role='Job Application Assistant',
            goal='Help with job application processes and form filling',
            backstory="""You are an experienced job application specialist who 
            knows the ins and outs of various job application platforms. You 
            can help fill out applications accurately and provide guidance on 
            application best practices.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
        
        # Outreach Specialist Agent
        self.agents['outreach_specialist'] = Agent(
            role='Professional Outreach Specialist',
            goal='Create compelling cold messages and cover letters',
            backstory="""You are a networking expert and professional communicator 
            who excels at crafting personalized, engaging messages that get responses. 
            You understand how to build professional relationships and make meaningful 
            connections.""",
            verbose=True,
            allow_delegation=False,
            llm=self.llm
        )
    
    def create_job_analysis_crew(self, job_url: str, user_id: str) -> Crew:
        """Create a crew for comprehensive job analysis"""
        
        # Task 1: Parse job description
        parse_job_task = Task(
            description=f"""
            Parse the job posting from this URL: {job_url}
            Extract all relevant information including:
            - Job title and company
            - Required skills and technologies
            - Experience requirements
            - Job responsibilities
            - Salary and benefits information
            - Application deadline
            
            Provide a comprehensive analysis of what this role requires.
            """,
            agent=self.agents['job_parser'],
            expected_output="Structured job information with all key details extracted and analyzed"
        )
        
        crew = Crew(
            agents=[self.agents['job_parser']],
            tasks=[parse_job_task],
            process=Process.sequential,
            verbose=True
        )
        
        return crew
    
    def create_resume_optimization_crew(self, job_data: Dict, resume_content: str, user_id: str) -> Crew:
        """Create a crew for comprehensive resume optimization"""
        
        # Task 1: Analyze resume against job requirements
        analyze_task = Task(
            description=f"""
            Analyze this resume against the job requirements:
            
            Job Details:
            {json.dumps(job_data, indent=2)}
            
            Resume Content:
            {resume_content}
            
            Provide a detailed analysis of:
            - Match score between resume and job
            - Missing skills and keywords
            - Strengths and weaknesses
            - Areas for improvement
            """,
            agent=self.agents['resume_analyzer'],
            expected_output="Comprehensive resume analysis with specific recommendations"
        )
        
        # Task 2: Optimize for ATS
        ats_optimize_task = Task(
            description="""
            Based on the resume analysis, optimize the resume for ATS systems:
            - Improve keyword density
            - Fix formatting issues
            - Ensure proper section headers
            - Optimize for parsing algorithms
            """,
            agent=self.agents['ats_optimizer'],
            expected_output="ATS-optimized resume with technical improvements",
            context=[analyze_task]
        )
        
        crew = Crew(
            agents=[self.agents['resume_analyzer'], self.agents['ats_optimizer']],
            tasks=[analyze_task, ats_optimize_task],
            process=Process.sequential,
            verbose=True
        )
        
        return crew
    
    def create_application_crew(self, job_data: Dict, resume_data: Dict, user_id: str) -> Crew:
        """Create a crew for complete job application process"""
        
        # Task 1: Create cover letter
        cover_letter_task = Task(
            description=f"""
            Create a compelling cover letter for this job application:
            
            Job Details:
            {json.dumps(job_data, indent=2)}
            
            Candidate Resume Summary:
            {resume_data.get('summary', 'No summary available')}
            
            The cover letter should:
            - Be personalized to the company and role
            - Highlight relevant experience
            - Show enthusiasm for the position
            - Be professional yet engaging
            """,
            agent=self.agents['outreach_specialist'],
            expected_output="Professional, personalized cover letter"
        )
        
        # Task 2: Prepare application materials
        application_prep_task = Task(
            description="""
            Prepare all application materials and provide guidance:
            - Review cover letter for consistency with resume
            - Suggest any additional documents needed
            - Provide application submission checklist
            - Recommend follow-up timeline
            """,
            agent=self.agents['application_assistant'],
            expected_output="Complete application package with submission guidance",
            context=[cover_letter_task]
        )
        
        crew = Crew(
            agents=[self.agents['outreach_specialist'], self.agents['application_assistant']],
            tasks=[cover_letter_task, application_prep_task],
            process=Process.sequential,
            verbose=True
        )
        
        return crew
    
    async def run_job_analysis_workflow(self, job_url: str, user_id: str) -> Dict[str, Any]:
        """Run the complete job analysis workflow"""
        try:
            crew = self.create_job_analysis_crew(job_url, user_id)
            result = crew.kickoff()
            
            return {
                "status": "success",
                "workflow": "job_analysis",
                "result": result,
                "user_id": user_id
            }
        except Exception as e:
            return {
                "status": "error",
                "workflow": "job_analysis",
                "error": str(e),
                "user_id": user_id
            }
    
    async def run_resume_optimization_workflow(self, job_data: Dict, resume_content: str, user_id: str) -> Dict[str, Any]:
        """Run the complete resume optimization workflow"""
        try:
            crew = self.create_resume_optimization_crew(job_data, resume_content, user_id)
            result = crew.kickoff()
            
            return {
                "status": "success",
                "workflow": "resume_optimization",
                "result": result,
                "user_id": user_id
            }
        except Exception as e:
            return {
                "status": "error",
                "workflow": "resume_optimization",
                "error": str(e),
                "user_id": user_id
            }
    
    async def run_application_workflow(self, job_data: Dict, resume_data: Dict, user_id: str) -> Dict[str, Any]:
        """Run the complete application workflow"""
        try:
            crew = self.create_application_crew(job_data, resume_data, user_id)
            result = crew.kickoff()
            
            return {
                "status": "success",
                "workflow": "application",
                "result": result,
                "user_id": user_id
            }
        except Exception as e:
            return {
                "status": "error",
                "workflow": "application",
                "error": str(e),
                "user_id": user_id
            }


# Global crew orchestrator instance
crew_orchestrator = CareerFlyCrew()
