{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/postcss": "^4.1.7", "react": "^19.1.0", "react-dom": "^19.1.0", "zustand": "^5.0.2", "framer-motion": "^12.0.0", "lottie-react": "^2.4.0", "@react-three/fiber": "^8.17.10", "@react-three/drei": "^9.117.3", "three": "^0.171.0", "react-router-dom": "^7.1.1", "lucide-react": "^0.468.0", "react-hot-toast": "^2.4.1", "react-dropzone": "^14.3.5", "axios": "^1.7.9"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}