/**
 * CareerFly Zustand Store
 * Global state management for the application
 */
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// Types
export interface User {
  id: string;
  email: string;
  name: string;
  api_keys?: Record<string, string>;
}

export interface Job {
  _id: string;
  user_id: string;
  url: string;
  title: string;
  company: string;
  description: string;
  requirements: string[];
  skills: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  parsed_data: Record<string, any>;
  created_at: string;
}

export interface Resume {
  _id: string;
  user_id: string;
  filename: string;
  content: string;
  file_path: string;
  version: number;
  job_id?: string;
  is_active: boolean;
  created_at: string;
}

export interface AgentTask {
  task_id: string;
  agent_type: string;
  status: 'running' | 'completed' | 'failed';
  input_data: Record<string, any>;
  output_data?: Record<string, any>;
  error_message?: string;
  execution_time?: number;
  created_at: string;
}

export interface Application {
  _id: string;
  user_id: string;
  job_id: string;
  resume_id: string;
  status: 'draft' | 'submitted' | 'rejected' | 'interview';
  cover_letter: string;
  application_data: Record<string, any>;
  created_at: string;
}

// Store interface
interface CareerFlyStore {
  // User state
  user: User | null;
  isAuthenticated: boolean;
  
  // Jobs state
  jobs: Job[];
  currentJob: Job | null;
  
  // Resumes state
  resumes: Resume[];
  currentResume: Resume | null;
  
  // Agent tasks state
  agentTasks: AgentTask[];
  activeAgents: string[];
  
  // Applications state
  applications: Application[];
  
  // UI state
  isLoading: boolean;
  error: string | null;
  currentView: 'dashboard' | 'jobs' | 'resumes' | 'applications' | 'settings';
  
  // Actions
  setUser: (user: User | null) => void;
  setJobs: (jobs: Job[]) => void;
  addJob: (job: Job) => void;
  setCurrentJob: (job: Job | null) => void;
  setResumes: (resumes: Resume[]) => void;
  addResume: (resume: Resume) => void;
  setCurrentResume: (resume: Resume | null) => void;
  setAgentTasks: (tasks: AgentTask[]) => void;
  addAgentTask: (task: AgentTask) => void;
  updateAgentTask: (taskId: string, updates: Partial<AgentTask>) => void;
  setActiveAgents: (agents: string[]) => void;
  setApplications: (applications: Application[]) => void;
  addApplication: (application: Application) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentView: (view: CareerFlyStore['currentView']) => void;
  clearStore: () => void;
}

// Create the store
export const useCareerFlyStore = create<CareerFlyStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        isAuthenticated: false,
        jobs: [],
        currentJob: null,
        resumes: [],
        currentResume: null,
        agentTasks: [],
        activeAgents: [],
        applications: [],
        isLoading: false,
        error: null,
        currentView: 'dashboard',

        // Actions
        setUser: (user) => set({ user, isAuthenticated: !!user }),
        
        setJobs: (jobs) => set({ jobs }),
        addJob: (job) => set((state) => ({ jobs: [job, ...state.jobs] })),
        setCurrentJob: (currentJob) => set({ currentJob }),
        
        setResumes: (resumes) => set({ resumes }),
        addResume: (resume) => set((state) => ({ resumes: [resume, ...state.resumes] })),
        setCurrentResume: (currentResume) => set({ currentResume }),
        
        setAgentTasks: (agentTasks) => set({ agentTasks }),
        addAgentTask: (task) => set((state) => ({ 
          agentTasks: [task, ...state.agentTasks],
          activeAgents: task.status === 'running' 
            ? [...new Set([...state.activeAgents, task.agent_type])]
            : state.activeAgents
        })),
        updateAgentTask: (taskId, updates) => set((state) => ({
          agentTasks: state.agentTasks.map(task => 
            task.task_id === taskId ? { ...task, ...updates } : task
          ),
          activeAgents: updates.status === 'completed' || updates.status === 'failed'
            ? state.activeAgents.filter(agent => {
                const updatedTask = state.agentTasks.find(t => t.task_id === taskId);
                return updatedTask ? agent !== updatedTask.agent_type : true;
              })
            : state.activeAgents
        })),
        setActiveAgents: (activeAgents) => set({ activeAgents }),
        
        setApplications: (applications) => set({ applications }),
        addApplication: (application) => set((state) => ({ 
          applications: [application, ...state.applications] 
        })),
        
        setLoading: (isLoading) => set({ isLoading }),
        setError: (error) => set({ error }),
        setCurrentView: (currentView) => set({ currentView }),
        
        clearStore: () => set({
          user: null,
          isAuthenticated: false,
          jobs: [],
          currentJob: null,
          resumes: [],
          currentResume: null,
          agentTasks: [],
          activeAgents: [],
          applications: [],
          isLoading: false,
          error: null,
          currentView: 'dashboard'
        })
      }),
      {
        name: 'careerfly-store',
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
          currentView: state.currentView
        })
      }
    ),
    {
      name: 'CareerFly Store'
    }
  )
);

// Selectors for better performance
export const useUser = () => useCareerFlyStore((state) => state.user);
export const useJobs = () => useCareerFlyStore((state) => state.jobs);
export const useResumes = () => useCareerFlyStore((state) => state.resumes);
export const useAgentTasks = () => useCareerFlyStore((state) => state.agentTasks);
export const useActiveAgents = () => useCareerFlyStore((state) => state.activeAgents);
export const useApplications = () => useCareerFlyStore((state) => state.applications);
export const useIsLoading = () => useCareerFlyStore((state) => state.isLoading);
export const useError = () => useCareerFlyStore((state) => state.error);
export const useCurrentView = () => useCareerFlyStore((state) => state.currentView);
